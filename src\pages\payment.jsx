import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function Payment() {
  const location = useLocation();
  const navigate = useNavigate();
  const bookingData = location.state?.bookingData;

  const [paymentData, setPaymentData] = useState({
    email: '',
    noTelp: '',
    jumlahOrang: bookingData?.guests || 3,
    extraNo: bookingData?.extraNo || '',
    tanggalBooking: bookingData?.date || '',
    activity1: '',
    activity2: '',
    activity3: '',
    activity4: ''
  });

  const paymentMethods = [
    { name: '<PERSON>', logo: '💳' },
    { name: 'ShopeePay', logo: '🛒' },
    { name: 'BRI', logo: '🏦' },
    { name: 'QRIS', logo: '📱' },
    { name: 'PayPal', logo: '💰' }
  ];

  const orderItems = bookingData?.selectedActivities || [
    { name: "Trekking and Jeep", price: 150000 },
    { name: "Swing and Nest", price: 150000 },
    { name: "Rafting", price: 150000 },
    { name: "ATV Ride", price: 150000 }
  ];

  const total = orderItems.reduce((sum, item) => sum + item.price, 0);

  const handleChange = (e) => {
    setPaymentData({
      ...paymentData,
      [e.target.name]: e.target.value
    });
  };

  const handlePayNow = () => {
    if (!paymentData.email || !paymentData.noTelp) {
      alert('Please fill in email and phone number');
      return;
    }
    
    // Simulate payment processing
    alert('Payment successful! Your booking has been confirmed.');
    
    // Save booking to localStorage for history
    const bookings = JSON.parse(localStorage.getItem('bookings') || '[]');
    const newBooking = {
      id: Date.now(),
      ...bookingData,
      paymentData,
      status: 'confirmed',
      paymentDate: new Date().toISOString()
    };
    bookings.push(newBooking);
    localStorage.setItem('bookings', JSON.stringify(bookings));
    
    navigate('/history');
  };

  if (!bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">No booking data found</h2>
          <button 
            onClick={() => navigate('/booking')}
            className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
          >
            Go to Booking
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Side - Payment Form */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="flex items-center mb-6">
                <span className="text-2xl mr-3">💳</span>
                <h2 className="text-2xl font-bold text-ubud-dark-green">Payment</h2>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <input
                      type="email"
                      name="email"
                      value={paymentData.email}
                      onChange={handleChange}
                      placeholder="Email"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ubud-dark-green"
                    />
                  </div>
                  <div>
                    <input
                      type="text"
                      name="jumlahOrang"
                      value={paymentData.jumlahOrang}
                      onChange={handleChange}
                      placeholder="Jumlah Orang"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ubud-dark-green"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <input
                      type="tel"
                      name="noTelp"
                      value={paymentData.noTelp}
                      onChange={handleChange}
                      placeholder="No Telp"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ubud-dark-green"
                    />
                  </div>
                  <div>
                    <input
                      type="text"
                      name="tanggalBooking"
                      value={paymentData.tanggalBooking}
                      onChange={handleChange}
                      placeholder="Tanggal Booking"
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ubud-dark-green"
                    />
                  </div>
                </div>

                <div>
                  <input
                    type="text"
                    name="extraNo"
                    value={paymentData.extraNo}
                    onChange={handleChange}
                    placeholder="Extra No"
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ubud-dark-green"
                  />
                </div>

                {/* Activity Dropdowns */}
                {[1, 2, 3, 4].map((num) => (
                  <div key={num}>
                    <select
                      name={`activity${num}`}
                      value={paymentData[`activity${num}`]}
                      onChange={handleChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-ubud-dark-green"
                    >
                      <option value="">Activity {num}</option>
                      <option value="trekking">Trekking and Jeep</option>
                      <option value="swing">Swing and Nest</option>
                      <option value="rafting">Rafting</option>
                      <option value="atv">ATV Ride</option>
                    </select>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Side - Order Summary */}
            <div className="bg-ubud-dark-green rounded-lg p-8 text-white">
              <div className="flex items-center mb-6">
                <span className="text-2xl mr-3">🛒</span>
                <h2 className="text-2xl font-bold">Your Order</h2>
              </div>

              <div className="space-y-4 mb-6">
                {orderItems.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span>{item.name}</span>
                    <span>Rp. {item.price.toLocaleString('id-ID')}</span>
                  </div>
                ))}
              </div>

              <div className="border-t border-ubud-light-green pt-4 mb-6">
                <div className="flex justify-between items-center text-xl font-bold">
                  <span>Total</span>
                  <span>Rp. {total.toLocaleString('id-ID')}</span>
                </div>
              </div>

              <button
                onClick={handlePayNow}
                className="w-full bg-white text-ubud-dark-green py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors mb-6"
              >
                Pay Now
              </button>

              <div>
                <h3 className="text-lg font-semibold mb-4">Available Payment Methods</h3>
                <div className="flex flex-wrap gap-3">
                  {paymentMethods.map((method, index) => (
                    <div key={index} className="bg-white rounded-lg p-3 flex items-center space-x-2">
                      <span className="text-2xl">{method.logo}</span>
                      <span className="text-ubud-dark-green font-medium text-sm">{method.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
