import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function Payment() {
  const location = useLocation();
  const navigate = useNavigate();
  const bookingData = location.state?.bookingData;

  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [user, setUser] = useState(null);

  useEffect(() => {
    // Get user data from localStorage
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const paymentMethods = [
    {
      id: 'dana',
      name: '<PERSON>',
      logo: '💳',
      description: 'Pay with Dana e-wallet'
    },
    {
      id: 'shopee',
      name: 'ShopeePay',
      logo: '🛒',
      description: 'Pay with ShopeePay'
    },
    {
      id: 'bri',
      name: 'Bank BRI',
      logo: '🏦',
      description: 'Transfer via BRI'
    },
    {
      id: 'qris',
      name: 'QRIS',
      logo: '📱',
      description: 'Scan QR code to pay'
    },
    {
      id: 'paypal',
      name: 'PayPal',
      logo: '💰',
      description: 'Pay with PayPal'
    }
  ];

  const orderItems = bookingData?.selectedActivities || [];
  const total = bookingData?.total || 0;

  const handlePayNow = async () => {
    if (!selectedPaymentMethod) {
      alert('Please select a payment method');
      return;
    }

    setIsProcessing(true);

    // Simulate payment processing delay
    setTimeout(() => {
      // Save booking to localStorage for history
      const bookings = JSON.parse(localStorage.getItem('bookings') || '[]');
      const newBooking = {
        id: Date.now(),
        ...bookingData,
        paymentMethod: selectedPaymentMethod,
        status: 'confirmed',
        paymentDate: new Date().toISOString(),
        userId: user?.id
      };
      bookings.push(newBooking);
      localStorage.setItem('bookings', JSON.stringify(bookings));

      setIsProcessing(false);
      alert('Payment successful! Your booking has been confirmed.');
      navigate('/history');
    }, 2000);
  };

  if (!bookingData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-ubud-dark-green mb-4">No booking data found</h2>
          <button
            onClick={() => navigate('/booking')}
            className="bg-ubud-dark-green text-white px-6 py-3 rounded-lg hover:bg-ubud-light-green transition-colors"
          >
            Go to Booking
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-ubud-dark-green mb-2">Complete Your Payment</h1>
            <p className="text-gray-600">Review your booking details and choose your payment method</p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Side - Booking Details */}
            <div className="lg:col-span-1">
              <div className="bg-white rounded-xl shadow-lg p-6 sticky top-8">
                <h3 className="text-xl font-bold text-ubud-dark-green mb-4 flex items-center">
                  <span className="mr-2">📋</span>
                  Booking Details
                </h3>

                <div className="space-y-3 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Name:</span>
                    <span className="font-medium">{bookingData.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Phone:</span>
                    <span className="font-medium">{bookingData.phone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date:</span>
                    <span className="font-medium">{bookingData.date}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Guests:</span>
                    <span className="font-medium">{bookingData.guests} people</span>
                  </div>
                </div>

                <div className="border-t pt-4 mt-4">
                  <h4 className="font-semibold text-ubud-dark-green mb-3">Selected Activities</h4>
                  <div className="space-y-2">
                    {orderItems.map((item, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">{item.name}</span>
                        <span className="font-medium text-ubud-dark-green">
                          Rp. {item.price.toLocaleString('id-ID')}
                        </span>
                      </div>
                    ))}
                  </div>

                  <div className="border-t pt-3 mt-3">
                    <div className="flex justify-between items-center font-bold text-lg">
                      <span>Total</span>
                      <span className="text-ubud-dark-green">Rp. {total.toLocaleString('id-ID')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Order Summary */}
            <div className="bg-ubud-dark-green rounded-lg p-8 text-white">
              <div className="flex items-center mb-6">
                <span className="text-2xl mr-3">🛒</span>
                <h2 className="text-2xl font-bold">Your Order</h2>
              </div>

              <div className="space-y-4 mb-6">
                {orderItems.map((item, index) => (
                  <div key={index} className="flex justify-between items-center">
                    <span>{item.name}</span>
                    <span>Rp. {item.price.toLocaleString('id-ID')}</span>
                  </div>
                ))}
              </div>

              <div className="border-t border-ubud-light-green pt-4 mb-6">
                <div className="flex justify-between items-center text-xl font-bold">
                  <span>Total</span>
                  <span>Rp. {total.toLocaleString('id-ID')}</span>
                </div>
              </div>

              <button
                onClick={handlePayNow}
                className="w-full bg-white text-ubud-dark-green py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors mb-6"
              >
                Pay Now
              </button>

              <div>
                <h3 className="text-lg font-semibold mb-4">Available Payment Methods</h3>
                <div className="flex flex-wrap gap-3">
                  {paymentMethods.map((method, index) => (
                    <div key={index} className="bg-white rounded-lg p-3 flex items-center space-x-2">
                      <span className="text-2xl">{method.logo}</span>
                      <span className="text-ubud-dark-green font-medium text-sm">{method.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
