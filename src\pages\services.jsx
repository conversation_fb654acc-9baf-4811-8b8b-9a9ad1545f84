import React from "react";

export default function Services() {
  const services = [
    {
      id: 1,
      title: "White Water Rafting",
      description: "Experience the thrill of navigating through Bali's beautiful rivers with our professional guides.",
      image: "/api/placeholder/400/300",
      duration: "3-4 hours",
      difficulty: "Beginner to Intermediate",
      price: "From $35"
    },
    {
      id: 2,
      title: "ATV Adventure",
      description: "Explore Ubud's countryside and rice terraces on an exciting ATV ride through muddy trails.",
      image: "/api/placeholder/400/300",
      duration: "2-3 hours",
      difficulty: "All levels",
      price: "From $45"
    },
    {
      id: 3,
      title: "Jungle Trekking",
      description: "Discover hidden waterfalls and pristine nature on guided trekking adventures.",
      image: "/api/placeholder/400/300",
      duration: "4-6 hours",
      difficulty: "Moderate",
      price: "From $40"
    },
    {
      id: 4,
      title: "Jeep Safari",
      description: "Explore Bali's volcanic landscapes and traditional villages in our comfortable jeeps.",
      image: "/api/placeholder/400/300",
      duration: "Full day",
      difficulty: "Easy",
      price: "From $60"
    },
    {
      id: 5,
      title: "Swing & Nest Experience",
      description: "Enjoy breathtaking views from our famous jungle swings and bird nest photo spots.",
      image: "/api/placeholder/400/300",
      duration: "2-3 hours",
      difficulty: "Easy",
      price: "From $25"
    },
    {
      id: 6,
      title: "Cultural Village Tour",
      description: "Immerse yourself in authentic Balinese culture with visits to traditional villages.",
      image: "/api/placeholder/400/300",
      duration: "Half day",
      difficulty: "Easy",
      price: "From $30"
    }
  ];

  return (
    <div className="min-h-screen bg-ubud-cream">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold text-white mb-4">Our Services</h1>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Discover amazing adventures and cultural experiences in the heart of Ubud, Bali
          </p>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => (
              <div key={service.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <img
                  src={service.image}
                  alt={service.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="text-xl font-bold text-ubud-dark-green mb-2">{service.title}</h3>
                  <p className="text-gray-600 mb-4">{service.description}</p>

                  <div className="space-y-2 mb-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Duration:</span>
                      <span className="text-ubud-dark-green font-medium">{service.duration}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Difficulty:</span>
                      <span className="text-ubud-dark-green font-medium">{service.difficulty}</span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold text-ubud-yellow">{service.price}</span>
                    <button className="bg-ubud-dark-green text-white px-4 py-2 rounded-full hover:bg-ubud-light-green transition-colors">
                      Book Now
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-ubud-light-green py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">Ready for Your Adventure?</h2>
          <p className="text-white mb-8 max-w-2xl mx-auto">
            Contact us today to book your perfect Ubud experience. Our team is ready to help you create unforgettable memories.
          </p>
          <button className="bg-ubud-yellow text-ubud-dark-green px-8 py-3 rounded-full font-bold text-lg hover:bg-yellow-400 transition-colors">
            Contact Us Now
          </button>
        </div>
      </section>
    </div>
  );
}
