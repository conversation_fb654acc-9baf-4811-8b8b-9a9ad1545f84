import React, { useState } from "react";

export default function Booking() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    service: '',
    date: '',
    guests: 1,
    message: ''
  });

  const services = [
    'White Water Rafting',
    'ATV Adventure',
    'Jungle Trekking',
    'Jeep Safari',
    'Swing & Nest Experience',
    'Cultural Village Tour'
  ];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Handle form submission here
    console.log('Booking submitted:', formData);
    alert('Thank you for your booking! We will contact you soon.');
  };

  return (
    <div className="min-h-screen bg-ubud-cream">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-ubud-dark-green to-ubud-light-green py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold text-white mb-4">Book Your Adventure</h1>
          <p className="text-xl text-white max-w-2xl mx-auto">
            Ready to experience the best of Ubud? Fill out the form below and we'll get back to you soon!
          </p>
        </div>
      </section>

      {/* Booking Form */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-ubud-dark-green font-medium mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                    placeholder="Enter your full name"
                  />
                </div>

                <div>
                  <label className="block text-ubud-dark-green font-medium mb-2">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                    placeholder="Enter your email"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-ubud-dark-green font-medium mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                    placeholder="Enter your phone number"
                  />
                </div>

                <div>
                  <label className="block text-ubud-dark-green font-medium mb-2">
                    Number of Guests *
                  </label>
                  <select
                    name="guests"
                    value={formData.guests}
                    onChange={handleChange}
                    required
                    className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                  >
                    {[1,2,3,4,5,6,7,8,9,10].map(num => (
                      <option key={num} value={num}>{num} Guest{num > 1 ? 's' : ''}</option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-ubud-dark-green font-medium mb-2">
                  Select Service *
                </label>
                <select
                  name="service"
                  value={formData.service}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                >
                  <option value="">Choose a service...</option>
                  {services.map((service, index) => (
                    <option key={index} value={service}>{service}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-ubud-dark-green font-medium mb-2">
                  Preferred Date *
                </label>
                <input
                  type="date"
                  name="date"
                  value={formData.date}
                  onChange={handleChange}
                  required
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                />
              </div>

              <div>
                <label className="block text-ubud-dark-green font-medium mb-2">
                  Additional Message
                </label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleChange}
                  rows="4"
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ubud-light-green focus:border-transparent"
                  placeholder="Any special requests or questions?"
                ></textarea>
              </div>

              <button
                type="submit"
                className="w-full bg-ubud-dark-green text-white py-3 rounded-lg font-bold text-lg hover:bg-ubud-light-green transition-colors"
              >
                Submit Booking Request
              </button>
            </form>
          </div>
        </div>
      </section>

      {/* Contact Info */}
      <section className="bg-ubud-light-green py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold text-white mb-8">Need Help?</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-white">
            <div>
              <h3 className="font-bold mb-2">📞 Call Us</h3>
              <p>+62 123 456 789</p>
            </div>
            <div>
              <h3 className="font-bold mb-2">📧 Email Us</h3>
              <p><EMAIL></p>
            </div>
            <div>
              <h3 className="font-bold mb-2">💬 WhatsApp</h3>
              <p>+62 123 456 789</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
