import React, { useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";

export default function Booking() {
  const location = useLocation();
  const navigate = useNavigate();
  const selectedService = location.state?.selectedService;

  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    extraNo: '',
    date: '',
    guests: 3,
    mainActivity: selectedService?.name || '',
    otherActivity1: '',
    otherActivity2: ''
  });

  const [selectedActivities, setSelectedActivities] = useState([
    selectedService || null,
    null,
    null,
    null
  ]);

  const services = [
    { id: 1, name: "Trekking and Jeep", price: 150000 },
    { id: 2, name: "Swing and Nest", price: 150000 },
    { id: 3, name: "Rafting", price: 150000 },
    { id: 4, name: "ATV Ride", price: 150000 }
  ];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleActivityChange = (index, activityName) => {
    const activity = services.find(s => s.name === activityName);
    const newSelectedActivities = [...selectedActivities];
    newSelectedActivities[index] = activity;
    setSelectedActivities(newSelectedActivities);

    if (index === 0) {
      setFormData({ ...formData, mainActivity: activityName });
    } else if (index === 1) {
      setFormData({ ...formData, otherActivity1: activityName });
    } else if (index === 2) {
      setFormData({ ...formData, otherActivity2: activityName });
    }
  };

  const calculateTotal = () => {
    return selectedActivities
      .filter(activity => activity !== null)
      .reduce((total, activity) => total + activity.price, 0);
  };

  const handleMakeBook = () => {
    if (!formData.name || !formData.phone || !formData.date || !formData.mainActivity) {
      alert('Please fill in all required fields');
      return;
    }

    const bookingData = {
      ...formData,
      selectedActivities: selectedActivities.filter(a => a !== null),
      total: calculateTotal(),
      bookingDate: new Date().toISOString()
    };

    navigate('/payment', { state: { bookingData } });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-6 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="bg-ubud-dark-green rounded-lg p-8 text-white">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Side - Form */}
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white font-medium mb-2">Nama</label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      placeholder="Mutiaa"
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    />
                  </div>
                  <div>
                    <label className="block text-white font-medium mb-2">Jumlah Pesanan</label>
                    <div className="flex items-center">
                      <input
                        type="number"
                        name="guests"
                        value={formData.guests}
                        onChange={handleChange}
                        min="1"
                        className="w-16 px-4 py-3 rounded-l-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                      />
                      <span className="bg-ubud-light-green text-white px-4 py-3 rounded-r-lg font-medium">
                        Orang
                      </span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white font-medium mb-2">No. Telp</label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      placeholder="08123456789"
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    />
                  </div>
                  <div>
                    <label className="block text-white font-medium mb-2">Main Activity</label>
                    <select
                      name="mainActivity"
                      value={formData.mainActivity}
                      onChange={(e) => {
                        handleChange(e);
                        handleActivityChange(0, e.target.value);
                      }}
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    >
                      <option value="">Select Main Activity</option>
                      {services.map((service) => (
                        <option key={service.id} value={service.name}>
                          {service.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white font-medium mb-2">Extra No</label>
                    <input
                      type="text"
                      name="extraNo"
                      value={formData.extraNo}
                      onChange={handleChange}
                      placeholder="1"
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    />
                  </div>
                  <div>
                    <label className="block text-white font-medium mb-2">Other Activity</label>
                    <select
                      name="otherActivity1"
                      value={formData.otherActivity1}
                      onChange={(e) => {
                        handleChange(e);
                        handleActivityChange(1, e.target.value);
                      }}
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    >
                      <option value="">Choose Other Activity (Optional)</option>
                      {services.map((service) => (
                        <option key={service.id} value={service.name}>
                          {service.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-white font-medium mb-2">Tanggal Booking</label>
                    <input
                      type="date"
                      name="date"
                      value={formData.date}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    />
                  </div>
                  <div>
                    <label className="block text-white font-medium mb-2">Other Activity</label>
                    <select
                      name="otherActivity2"
                      value={formData.otherActivity2}
                      onChange={(e) => {
                        handleChange(e);
                        handleActivityChange(2, e.target.value);
                      }}
                      className="w-full px-4 py-3 rounded-lg text-gray-800 focus:outline-none focus:ring-2 focus:ring-ubud-yellow"
                    >
                      <option value="">Choose Other Activity (Optional)</option>
                      {services.map((service) => (
                        <option key={service.id} value={service.name}>
                          {service.name}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <button
                  onClick={handleMakeBook}
                  className="w-full bg-white text-ubud-dark-green py-4 rounded-lg font-bold text-lg hover:bg-gray-100 transition-colors"
                >
                  Make A Book
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
