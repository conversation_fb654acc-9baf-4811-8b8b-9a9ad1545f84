import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";

export default function Navbar() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [user, setUser] = useState(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Check if user is logged in
    const userData = localStorage.getItem('user');
    if (userData) {
      setIsLoggedIn(true);
      setUser(JSON.parse(userData));
    }
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('user');
    setIsLoggedIn(false);
    setUser(null);
    navigate('/');
  };

  return (
    <nav className="bg-ubud-dark-green shadow-lg px-6 py-4 flex justify-between items-center sticky top-0 z-50">
      <div className="flex items-center">
        <img src="/src/assets/logo-ubud.png" alt="Ubud Activity" className="h-10 w-auto mr-3" />
        <div className="text-white font-bold text-xl">
          ubud activity
        </div>
      </div>
      <div className="flex items-center space-x-8">
        <Link to="/" className="text-white hover:text-ubud-yellow font-medium transition-colors">
          Home
        </Link>
        <Link to="/services" className="text-white hover:text-ubud-yellow font-medium transition-colors">
          Services
        </Link>
        <Link to="/booking" className="text-white hover:text-ubud-yellow font-medium transition-colors">
          Booking Appointment
        </Link>
        {isLoggedIn && (
          <Link to="/history" className="text-white hover:text-ubud-yellow font-medium transition-colors">
            Riwayat Pemesanan
          </Link>
        )}
        <Link to="/about" className="text-white hover:text-ubud-yellow font-medium transition-colors">
          About Us
        </Link>
        {!isLoggedIn ? (
          <Link
            to="/login"
            className="bg-white text-ubud-dark-green px-6 py-2 rounded-full hover:bg-ubud-cream transition-colors font-medium"
          >
            Login
          </Link>
        ) : (
          <div className="relative group">
            <button className="bg-white text-ubud-dark-green px-6 py-2 rounded-full hover:bg-ubud-cream transition-colors font-medium">
              Dashboard
            </button>
            <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
              <div className="py-1">
                <div className="px-4 py-2 text-sm text-gray-700 border-b">
                  Welcome, {user?.name || 'User'}
                </div>
                <Link to="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  Profile
                </Link>
                <button
                  onClick={handleLogout}
                  className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  Logout
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
